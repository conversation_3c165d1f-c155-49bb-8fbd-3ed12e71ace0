<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="64dp"
    android:height="64dp"
    android:viewportWidth="64"
    android:viewportHeight="64">

    <!-- Background circle -->
    <path
        android:fillColor="#607D8B"
        android:pathData="M32,8 C42.4,8 51,16.6 51,27 C51,37.4 42.4,46 32,46 C21.6,46 13,37.4 13,27 C13,16.6 21.6,8 32,8 Z" />

    <!-- Person head -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M32,18 C35.3,18 38,20.7 38,24 C38,27.3 35.3,30 32,30 C28.7,30 26,27.3 26,24 C26,20.7 28.7,18 32,18 Z" />

    <!-- Person body -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M32,32 C26,32 21,35 21,39 L21,42 C21,43.1 21.9,44 23,44 L41,44 C42.1,44 43,43.1 43,42 L43,39 C43,35 38,32 32,32 Z" />

    <!-- Profile card background -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M16,14 L48,14 C49.1,14 50,14.9 50,16 L50,18 C50,19.1 49.1,20 48,20 L16,20 C14.9,20 14,19.1 14,18 L14,16 C14,14.9 14.9,14 16,14 Z"
        android:fillAlpha="0.3" />
</vector>
